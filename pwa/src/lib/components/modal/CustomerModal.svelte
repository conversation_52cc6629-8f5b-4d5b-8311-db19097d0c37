<script lang="ts">
    import {t} from 'svelte-i18n';
    import {createEventDispatcher} from 'svelte';
    import {profile} from '$lib/stores';
    import {Customer_jsonld_customer_read} from '$lib/zodios';
    import {goto} from '$app/navigation';

    import Modal from './Modal.svelte';
    import CustomerForm from "$lib/components/customer/CustomerForm.svelte";

    export let customer: Customer_jsonld_customer_read | null = null;
    export let showModal = false;
    export let edition = false;

    const dispatch = createEventDispatcher<{
        created: { customer: Customer_jsonld_customer_read };
        saved: { customer: Customer_jsonld_customer_read };
    }>();

    let modal: Modal;
    let company = $profile.company;

    function onSubmitted(customer: Customer_jsonld_customer_read) {
        modal?.close();
        if (edition) {
            dispatch('saved', {customer});
        } else if (customer) {
            dispatch('created', {customer});
        }
    }

    function onRemoved() {
        modal?.close();
        goto(`/dashboard`);
    }
</script>

<Modal bind:this={modal} bind:showModal>
    <h4 slot="header" class="fw-bold">
        {#if edition}
            {$t('modal.customer.title')}
        {:else}
            {$t('modal.customer.title-edit')}
        {/if}
    </h4>

    {#if showModal}
        <CustomerForm
            {edition}
            {customer}
            {company}
            {modal}
            {onSubmitted}
            {onRemoved}
        />
    {/if}
</Modal>
