<script lang="ts">
    import { t } from 'svelte-i18n';
    import { createZodiosForm, type CustomErrorMap } from '$lib/form';
    import { zodiosClient } from '$lib/stores';
    import { Customer_jsonld_customer_write, Customer_jsonld_customer_read } from '$lib/zodios';

    import Radio from '$lib/components/form/Radio.svelte';
    import Input from '$lib/components/form/Input.svelte';
    import ButtonIcon from '../ui/ButtonIcon.svelte';
    import { faCircleCheck, faTrash } from '@fortawesome/free-solid-svg-icons';
    import CityAutocomplete from '../form/CityAutocomplete.svelte';
    import PhoneNumber from '../form/PhoneNumber.svelte';
    import { execIfIsGrantedOrAskForPinCode } from '$lib/auth';
    import toast from 'svelte-french-toast';
    import { z } from 'zod';
    import type Modal from "$lib/components/modal/Modal.svelte";

    export let customer: Customer_jsonld_customer_read | null = null;
    export let edition = false;

    export let modal: Modal|null = null;
    export let company: {
        id: string;
        '@id': string;
    };
    export let onSubmitted = (customer: Customer_jsonld_customer_read) => {};
    export let onRemoved = () => {};
    export let anonymousToken: string | null = null;
    export let anonymousPost = false;

    let savedCustomer: Customer_jsonld_customer_read;

    const messages: CustomErrorMap<typeof Customer_jsonld_customer_write> = {
        firstname: $t('modal.customer.errors.firstname'),
        lastname: $t('modal.customer.errors.lastname'),
        phoneNumber: $t('modal.customer.errors.phoneNumber'),
        email: $t('modal.customer.errors.email')
    };

    const schema = Customer_jsonld_customer_write.extend({
        email: z.string().max(255).email().nullish().or(z.literal('')).nullish(),
        postalCode: z.string().min(5).max(5).nullish().or(z.literal('')).nullish()
    });

    const initialData = {
        lastname: '',
        firstname: '',
        phoneNumber: '',
        phoneNumberCountryCode: '',
        birthDate: '',
        email: '',
        postalCode: '',
        city: '',
        note: ''
    };

    let phoneNumberInput: PhoneNumber<typeof schema>;
    let countryName: string;

    const form = createZodiosForm({
        schema: schema,
        async onSubmitted(data) {
            if (data.email === '') {
                data.email = null;
            }
            if (data.postalCode === '') {
                data.postalCode = null;
            }

            if (data.phoneNumber) {
                data.phoneNumber = data.phoneNumber.replaceAll(' ', '');
            }

            if (edition && customer?.id) {
                savedCustomer = await $zodiosClient.customerPatch(data, {
                    params: {
                        id: customer.id.toString(),
                        companyId: company.id.toString()
                    }
                });
                return savedCustomer;
            }
            data.company = company['@id'];
            const response = await $zodiosClient[anonymousPost ? 'customerCreateWithToken' : 'customerPost'](data, {
                params: { companyId: company.id.toString() },
                queries: anonymousPost && anonymousToken ? { token: anonymousToken } : {}
            });
            customer = response;
            return response;
        },
        initialData: customer ? Object.assign({}, customer) : initialData,
        options: {
            id: 'customer-modal-' + (edition ? 'edition' : 'creation'),
            taintedMessage: null,
            onUpdated() {
                if (edition) {
                    toast.success($t('modal.customer.toast-success'));
                } else {
                    toast.success($t('modal.customer.toast-success-create'));
                }
                onSubmitted(edition && customer?.id ? savedCustomer : customer);
                modal?.close();
            }
        },
        messages
    });

    const removeClient = async () => {
        execIfIsGrantedOrAskForPinCode('customerDelete', async () => {
            if (edition && customer?.id && confirm($t('modal.customer.remove.alert'))) {
                try {
                    await $zodiosClient.customerDelete(undefined, {
                        params: {
                            id: customer.id.toString(),
                            companyId: company.id.toString()
                        }
                    });
                    onRemoved();

                    toast.success($t('modal.customer.remove.toast-success'));
                } catch (e) {
                    toast.error($t('modal.customer.remove.toast-error'));
                    throw e;
                }
            }
        });
    };

    const { enhance, message, form: _form, delayed } = form;

    $: {
        // On vide les champs de code postal et ville si le pays n'est pas FR car pas le droit de les remplir
        // On recalcul également le nom du pays qui est écrit en dur dans le formulaire
        if ($_form && $_form.phoneNumberCountryCode) {
            if ($_form.phoneNumberCountryCode !== 'fr') {
                $_form.postalCode = '';
                $_form.city = '';
            }
            if (phoneNumberInput) {
                countryName = phoneNumberInput.getSelectedCountryData().name;
            }
        }
    }
</script>

{#if $message}
    <div class="alert alert-danger">{$message}</div>
{/if}

<form
    method="POST"
    use:enhance
    class="form-labels-bold"
    data-testid="customer-{edition ? 'edit' : 'create'}-form"
    autocomplete="off"
>
    <div class="mb-3 text-center">
        <Radio {form} containerClass="mb-5" field="civility" />
    </div>
    <div class="row">
        <div class="col-lg-6">
            <Input
                {form}
                containerClass="mb-5"
                field="lastname"
                label={$t('modal.customer.form.nom')}
                autocomplete="customer-lastname"
            />
        </div>
        <div class="col-lg-6">
            <Input
                {form}
                containerClass="mb-5"
                field="firstname"
                label={$t('modal.customer.form.firstname')}
                autocomplete="customer-firstname"
            />
        </div>
    </div>
    <div class="row">
        <div class="col-lg-6">
            <PhoneNumber
                {form}
                containerClass="mb-5"
                field="phoneNumber"
                countryCodeField="phoneNumberCountryCode"
                bind:countryCode={$_form.phoneNumberCountryCode}
                bind:this={phoneNumberInput}
                label={$t('modal.customer.form.phoneNumber')}
                autocomplete="customer-tel"
                type="tel"
                required
            />
        </div>
        <div class="col-lg-6">
            <Input
                {form}
                containerClass="mb-5"
                field="birthDate"
                label={$t('modal.customer.form.birthDate')}
                type="date"
                max="2100-01-01"
                autocomplete="customer-birthdate"
            />
        </div>
    </div>
    <div class="row">
        <div class="col-lg-6">
            <Input
                {form}
                containerClass="mb-5"
                field="email"
                label={$t('modal.customer.form.email')}
                type="email"
                autocomplete="customer-email"
            />
        </div>
    </div>
    <div class="row">
        <div class="col-lg-6">
            <Input
                {form}
                containerClass="mb-5"
                field="postalCode"
                label={$t('modal.customer.form.postalCode')}
                autocomplete="customer-postalcode"
                disabled={$_form.phoneNumberCountryCode !== 'fr'}
            />
        </div>
        <div class="col-lg-6">
            <CityAutocomplete
                {form}
                containerClass="mb-5"
                field="city"
                label={$t('modal.customer.form.city')}
                postalCodeField="postalCode"
                autocomplete="customer-city"
                disabled={$_form.phoneNumberCountryCode !== 'fr'}
            />
        </div>
    </div>
    <div class="row">
        <div class="mb-5">
            <b>{$t('modal.customer.form.country')}</b>
            {countryName}
        </div>
    </div>
    {#if !anonymousPost}
        <div class="row">
            <div class="col-lg-12">
                <Input
                    {form}
                    containerClass="mb-5"
                    field="note"
                    label={$t('modal.customer.form.note')}
                />
            </div>
        </div>
    {/if}
    <div
        class="flex-center-x flex-column flex-sm-row gap-3 gap-sm-0"
        class:justify-content-between={edition}
        class:justify-content-end={!edition}
    >
        {#if edition}
            <ButtonIcon
                theme="dark"
                size="large"
                icon={faTrash}
                on:click={removeClient}
                label={$t('modal.customer.form.remove')}
            />
        {/if}
        <ButtonIcon
            theme="pink"
            size="large"
            icon={faCircleCheck}
            label={$t('general.save')}
            type="submit"
            loader={$delayed}
        />
    </div>
</form>
