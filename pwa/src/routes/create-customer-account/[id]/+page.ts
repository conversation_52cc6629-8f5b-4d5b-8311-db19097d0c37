import { AxiosError } from 'axios';
import type { PageLoad } from './$types';

export const load = (async ({ parent, url, params }) => {
	const { zodiosClient } = await parent();

	const token = url.searchParams.get('token');

	let page = null;
	let error = null;

	if (token === null) {
		error = 'Le token de sécurité est invalide';
	} else {
		try {
			page = await zodiosClient.getCreateCustomerWithTokenInfos({ params: { id: params.id }, queries: { token } });
		} catch (e) {
			if (e instanceof AxiosError) {
				if (e.code === '403') {
					error = 'Le token de sécurité est invalide';
				} else {
					error = "Une erreur s'est produite";
				}
			}
		}
	}

	return {
		error,
		company: {
			id: params.id,
			'@id': '/api/companies/' + params.id,
			name: page?.company,
		},
		token,
	};
}) satisfies PageLoad;
