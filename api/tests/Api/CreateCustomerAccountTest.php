<?php

namespace App\Tests\Api;

use App\Controller\Company\GenerateQrCode\CreateCustomerWithTokenInfosController;
use App\Entity\Company;
use App\Entity\Customer;
use App\Factory\CompanyFactory;
use App\Factory\CustomerFactory;
use PHPUnit\Framework\Attributes\Group;
use Zenstruck\Foundry\Persistence\Proxy;

class CreateCustomerAccountTest extends AbstractTestCase
{
    /** @var Proxy<Company>|Company */
    private Proxy|Company $company;
    /** @var Proxy<Company>|Company */
    private Proxy|Company $anotherCompany;
    private string $validToken;
    private string $invalidToken;

    public function setUp(): void
    {
        $this->company = CompanyFactory::createOne();
        $this->anotherCompany = CompanyFactory::createOne();

        // Generate valid token for the company
        $this->validToken = CreateCustomerWithTokenInfosController::createToken($this->company);
        $this->invalidToken = 'invalid-token-123';
    }

    /**
     * GET /api/companies/{id}/create-customer-with-token-infos -> 200 with valid token.
     */
    public function testGetCreateCustomerWithTokenInfosWithValidToken(): void
    {
        $this->browser()
            ->get(sprintf('/api/companies/%s/create-customer-with-token-infos?token=%s', $this->company->getId(), $this->validToken))
            ->assertStatus(200)
            ->assertJson()
            ->assertJsonContains([
                'company' => $this->company->getName(),
            ]);
    }

    /**
     * GET /api/companies/{id}/create-customer-with-token-infos -> 403 with invalid token.
     */
    public function testGetCreateCustomerWithTokenInfosWithInvalidToken(): void
    {
        $this->browser()
            ->get(sprintf('/api/companies/%s/create-customer-with-token-infos?token=%s', $this->company->getId(), $this->invalidToken))
            ->assertStatus(403);
    }

    /**
     * GET /api/companies/{id}/create-customer-with-token-infos -> 404 without token.
     */
    public function testGetCreateCustomerWithTokenInfosWithoutToken(): void
    {
        $this->browser()
            ->get(sprintf('/api/companies/%s/create-customer-with-token-infos', $this->company->getId()))
            ->assertStatus(404);
    }

    /**
     * GET /api/companies/{id}/create-customer-with-token-infos -> 404 with non-existent company.
     */
    public function testGetCreateCustomerWithTokenInfosWithNonExistentCompany(): void
    {
        $this->browser()
            ->get(sprintf('/api/companies/999999/create-customer-with-token-infos?token=%s', $this->validToken))
            ->assertStatus(404);
    }

    /**
     * POST /companies/{companyId}/customers/create-with-token -> 201 with valid token and data.
     */
    #[Group('CustomerHistory')]
    public function testCreateCustomerAccountAnonymousWithValidData(): void
    {
        $customer = $this->browser()
            ->post(sprintf('/api/companies/%s/customers/create-with-token?token=%s', $this->company->getId(), $this->validToken), [
                'json' => [
                    'company' => $this->iri($this->company),
                    'civility' => 'Monsieur',
                    'lastname' => 'Dupont',
                    'firstname' => 'Jean',
                    'phoneNumber' => '**********',
                    'phoneNumberCountryCode' => 'fr',
                    'birthDate' => '1990-05-15',
                    'email' => '<EMAIL>',
                    'postalCode' => '75001',
                    'city' => 'Paris',
                    'note' => 'Client créé via QR code',
                ],
            ])
            ->assertStatus(201)
            ->assertJson()
            ->assertJsonContains([
                '@context' => '/contexts/Customer',
                '@type' => 'Customer',
                'civility' => 'Monsieur',
                'lastname' => 'Dupont',
                'firstname' => 'Jean',
                'phoneNumber' => '*********',
                'phoneNumberCountryCode' => 'fr',
                'birthDate' => '1990-05-15',
                'email' => '<EMAIL>',
                'postalCode' => '75001',
                'city' => 'Paris',
                'note' => 'Client créé via QR code',
            ])
            ->assertMatchesResourceJsonSchemas(Customer::class, Customer::OPERATION_CREATE_WITH_TOKEN)
            ->json()
            ->decoded();

        // Verify the customer was created and belongs to the correct company
        $createdCustomer = CustomerFactory::find($customer['id']);
        $this->assertEquals($this->company->getId(), $createdCustomer->getCompany()->getId());
    }

    /**
     * POST /companies/{companyId}/customers/create-with-token -> 201 with minimal required data.
     */
    public function testCreateCustomerAccountAnonymousWithMinimalData(): void
    {
        $this->browser()
            ->post(sprintf('/api/companies/%s/customers/create-with-token?token=%s', $this->company->getId(), $this->validToken), [
                'json' => [
                    'company' => $this->iri($this->company),
                    'civility' => 'Madame',
                    'lastname' => 'Martin',
                    'firstname' => 'Marie',
                    'phoneNumber' => '0*********',
                    'phoneNumberCountryCode' => 'fr',
                ],
            ])
            ->assertStatus(201)
            ->assertJson()
            ->assertJsonContains([
                '@context' => '/contexts/Customer',
                '@type' => 'Customer',
                'civility' => 'Madame',
                'lastname' => 'Martin',
                'firstname' => 'Marie',
                'phoneNumber' => '*********',
                'phoneNumberCountryCode' => 'fr',
            ])
            ->assertMatchesResourceJsonSchemas(Customer::class, Customer::OPERATION_CREATE_WITH_TOKEN);
    }

    /**
     * POST /companies/{companyId}/customers/create-with-token -> 403 with invalid token.
     */
    public function testCreateCustomerAccountAnonymousWithInvalidToken(): void
    {
        $this->browser()
            ->post(sprintf('/api/companies/%s/customers/create-with-token?token=%s', $this->company->getId(), $this->invalidToken), [
                'json' => [
                    'company' => $this->iri($this->company),
                    'civility' => 'Monsieur',
                    'lastname' => 'Dupont',
                    'firstname' => 'Jean',
                    'phoneNumber' => '**********',
                    'phoneNumberCountryCode' => 'fr',
                ],
            ])
            ->assertStatus(403);
    }

    /**
     * POST /companies/{companyId}/customers/create-with-token -> 403 without token.
     */
    public function testCreateCustomerAccountAnonymousWithoutToken(): void
    {
        $this->browser()
            ->post(sprintf('/api/companies/%s/customers/create-with-token', $this->company->getId()), [
                'json' => [
                    'company' => $this->iri($this->company),
                    'civility' => 'Monsieur',
                    'lastname' => 'Dupont',
                    'firstname' => 'Jean',
                    'phoneNumber' => '**********',
                    'phoneNumberCountryCode' => 'fr',
                ],
            ])
            ->assertStatus(403);
    }

    /**
     * POST /companies/{companyId}/customers/create-with-token -> 403 with wrong company in token.
     */
    public function testCreateCustomerAccountAnonymousWithWrongCompanyToken(): void
    {
        $wrongCompanyToken = CreateCustomerWithTokenInfosController::createToken($this->anotherCompany);

        $this->browser()
            ->post(sprintf('/api/companies/%s/customers/create-with-token?token=%s', $this->company->getId(), $wrongCompanyToken), [
                'json' => [
                    'company' => $this->iri($this->company),
                    'civility' => 'Monsieur',
                    'lastname' => 'Dupont',
                    'firstname' => 'Jean',
                    'phoneNumber' => '**********',
                    'phoneNumberCountryCode' => 'fr',
                ],
            ])
            ->assertStatus(403);
    }

    /**
     * POST /companies/{companyId}/customers/create-with-token -> 403 with mismatched company in payload.
     */
    public function testCreateCustomerAccountAnonymousWithMismatchedCompany(): void
    {
        $this->browser()
            ->post(sprintf('/api/companies/%s/customers/create-with-token?token=%s', $this->company->getId(), $this->validToken), [
                'json' => [
                    'company' => $this->iri($this->anotherCompany), // Different company in payload
                    'civility' => 'Monsieur',
                    'lastname' => 'Dupont',
                    'firstname' => 'Jean',
                    'phoneNumber' => '**********',
                    'phoneNumberCountryCode' => 'fr',
                ],
            ])
            ->assertStatus(403);
    }

    /**
     * POST /companies/{companyId}/customers/create-with-token -> 422 with invalid data.
     */
    public function testCreateCustomerAccountAnonymousWithInvalidData(): void
    {
        $this->browser()
            ->post(sprintf('/api/companies/%s/customers/create-with-token?token=%s', $this->company->getId(), $this->validToken), [
                'json' => [
                    'company' => $this->iri($this->company),
                    'civility' => 'InvalidCivility',
                    'lastname' => '', // Empty required field
                    'firstname' => '', // Empty required field
                    'phoneNumber' => 'invalid-phone',
                    'phoneNumberCountryCode' => 'fr',
                ],
            ])
            ->assertStatus(422)
            ->assertJsonContains([
                '@context' => '/contexts/ConstraintViolationList',
                '@type' => 'ConstraintViolationList',
                'hydra:title' => 'An error occurred',
            ]);
    }

    /**
     * POST /companies/{companyId}/customers/create-with-token -> 422 with existing phone number.
     */
    public function testCreateCustomerAccountAnonymousWithExistingPhoneNumber(): void
    {
        // Create an existing customer with the same phone number
        CustomerFactory::createOne([
            'phoneNumber' => '**********',
            'company' => $this->company,
        ]);

        $this->browser()
            ->post(sprintf('/api/companies/%s/customers/create-with-token?token=%s', $this->company->getId(), $this->validToken), [
                'json' => [
                    'company' => $this->iri($this->company),
                    'civility' => 'Monsieur',
                    'lastname' => 'Dupont',
                    'firstname' => 'Jean',
                    'phoneNumber' => '**********', // Same phone number
                    'phoneNumberCountryCode' => 'fr',
                ],
            ])
            ->assertStatus(422)
            ->assertJsonContains([
                '@context' => '/contexts/ConstraintViolationList',
                '@type' => 'ConstraintViolationList',
                'hydra:title' => 'An error occurred',
                'violations' => [
                    [
                        'propertyPath' => 'phoneNumber',
                        'message' => 'Un client possédant ce numéro de téléphone existe déjà.',
                    ],
                ],
            ]);
    }

    /**
     * POST /companies/{companyId}/customers/create-with-token -> 201 with existing phone number in another company.
     */
    public function testCreateCustomerAccountAnonymousWithExistingPhoneNumberInAnotherCompany(): void
    {
        // Create an existing customer with the same phone number in another company
        CustomerFactory::createOne([
            'phoneNumber' => '**********',
            'company' => $this->anotherCompany,
        ]);

        $this->browser()
            ->post(sprintf('/api/companies/%s/customers/create-with-token?token=%s', $this->company->getId(), $this->validToken), [
                'json' => [
                    'company' => $this->iri($this->company),
                    'civility' => 'Monsieur',
                    'lastname' => 'Dupont',
                    'firstname' => 'Jean',
                    'phoneNumber' => '**********', // Same phone number but different company
                    'phoneNumberCountryCode' => 'fr',
                ],
            ])
            ->assertStatus(201)
            ->assertMatchesResourceJsonSchemas(Customer::class, Customer::OPERATION_CREATE_WITH_TOKEN);
    }

    /**
     * POST /companies/{companyId}/customers/create-with-token -> 201 with international phone number.
     */
    public function testCreateCustomerAccountAnonymousWithInternationalPhoneNumber(): void
    {
        $this->browser()
            ->post(sprintf('/api/companies/%s/customers/create-with-token?token=%s', $this->company->getId(), $this->validToken), [
                'json' => [
                    'company' => $this->iri($this->company),
                    'civility' => 'Madame',
                    'lastname' => 'Schmidt',
                    'firstname' => 'Anna',
                    'phoneNumber' => '************',
                    'phoneNumberCountryCode' => 'de',
                    'email' => '<EMAIL>',
                ],
            ])
            ->assertStatus(201)
            ->assertJson()
            ->assertJsonContains([
                'phoneNumber' => '***********',
                'phoneNumberCountryCode' => 'de',
                'email' => '<EMAIL>',
            ])
            ->assertMatchesResourceJsonSchemas(Customer::class, Customer::OPERATION_CREATE_WITH_TOKEN);
    }

    /**
     * POST /companies/{companyId}/customers/create-with-token -> 422 with invalid phone number format.
     */
    public function testCreateCustomerAccountAnonymousWithInvalidPhoneNumberFormat(): void
    {
        $this->browser()
            ->post(sprintf('/api/companies/%s/customers/create-with-token?token=%s', $this->company->getId(), $this->validToken), [
                'json' => [
                    'company' => $this->iri($this->company),
                    'civility' => 'Monsieur',
                    'lastname' => 'Dupont',
                    'firstname' => 'Jean',
                    'phoneNumber' => '**********', // Belgian number with French country code
                    'phoneNumberCountryCode' => 'fr',
                ],
            ])
            ->assertStatus(422);
    }

    /**
     * POST /companies/{companyId}/customers/create-with-token -> 404 with non-existent company.
     */
    public function testCreateCustomerAccountAnonymousWithNonExistentCompany(): void
    {
        $this->browser()
            ->post(sprintf('/api/companies/999999/create-customer-account?token=%s', $this->validToken), [
                'json' => [
                    'company' => '/api/companies/999999',
                    'civility' => 'Monsieur',
                    'lastname' => 'Dupont',
                    'firstname' => 'Jean',
                    'phoneNumber' => '**********',
                    'phoneNumberCountryCode' => 'fr',
                ],
            ])
            ->assertStatus(404);
    }

    /**
     * Test token generation and validation methods.
     */
    public function testTokenGenerationAndValidation(): void
    {
        $token = CreateCustomerWithTokenInfosController::createToken($this->company);

        // Token should be 9 characters long
        $this->assertEquals(9, strlen($token));

        // Token should be valid for the company
        $this->assertTrue(CreateCustomerWithTokenInfosController::validateToken($token, $this->company));

        // Token should not be valid for another company
        $this->assertFalse(CreateCustomerWithTokenInfosController::validateToken($token, $this->anotherCompany));

        // Invalid token should not be valid
        $this->assertFalse(CreateCustomerWithTokenInfosController::validateToken('invalid', $this->company));
    }
}
