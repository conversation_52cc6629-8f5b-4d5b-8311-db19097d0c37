<?php

namespace App\Security\Voter;

use App\Controller\Company\GenerateQrCode\CreateCustomerWithTokenInfosController;
use App\Entity\Customer;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

/**
 * Gère la sécurité de la création de compte client sans JWT.
 */
class CustomerCreateWithTokenVoter extends Voter
{
    public const string RULE_POST_DENORMALIZE = "is_granted('CUSTOMER_CREATE_WITH_TOKEN', {customer: object, request: request})";
    public const string CREATE = 'CUSTOMER_CREATE_WITH_TOKEN';

    protected function supports(string $attribute, mixed $subject): bool
    {
        return self::CREATE == $attribute
            && \is_array($subject)
            && $subject['request'] instanceof Request
            && $subject['customer'] instanceof Customer;
    }

    /** @param string $subject */
    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token): bool
    {
        assert($subject['request'] instanceof Request);
        assert($subject['customer'] instanceof Customer);

        $token = $subject['request']->query->get('token');

        if (!$token) {
            throw new AccessDeniedHttpException('Missing token');
        }

        $companyId = $subject['request']->attributes->get('companyId');

        $company = $subject['customer']->getCompany();
        if (
            !CreateCustomerWithTokenInfosController::validateToken($token, $company)
        ) {
            throw new AccessDeniedHttpException('Invalid token');
        }

        if ((string) $company->getId() !== (string) $companyId) {
            throw new BadRequestHttpException('Invalid company id');
        }

        return true;
    }
}
