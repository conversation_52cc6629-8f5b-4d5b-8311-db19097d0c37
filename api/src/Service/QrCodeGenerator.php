<?php

namespace App\Service;

use App\Entity\Company;
use Endroid\QrCode\Builder\BuilderInterface;
use Endroid\QrCode\Writer\Result\ResultInterface;

readonly class QrCodeGenerator
{
    public function __construct(
        private FrontEndRouter $frontEndRouter,
        private BuilderInterface $defaultQrCodeBuilder,
    ) {
    }

    public function generateCompanyQrCode(Company $company): ResultInterface
    {
        $targetUrl = $this->getQrCodeTargetUrl($company);

        return $this->defaultQrCodeBuilder->build(
            data: $targetUrl,
        );
    }

    public function getQrCodeTargetUrl(Company $company): string
    {
        return $this->frontEndRouter->getCreateCustomerAccountUrl($company);
    }
}
