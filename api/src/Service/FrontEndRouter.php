<?php

namespace App\Service;

use App\Controller\Company\GenerateQrCode\CreateCustomerWithTokenInfosController;
use App\Entity\Company;
use App\Entity\Sms;
use CoopTilleuls\ForgotPasswordBundle\Entity\AbstractPasswordToken;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

class FrontEndRouter
{
    public function __construct(
        #[Autowire(env: 'FRONT_URL')] private readonly string $frontUrl,
        private readonly RichSmsManager $richSmsManager
    ) {
    }

    public function getBaseUrl(): string
    {
        return $this->frontUrl;
    }

    public function getValidateEmailUrl(string $token): string
    {
        return sprintf('%s/register/validate-email/%s', $this->frontUrl, $token);
    }

    public function getDashboardUrl(): string
    {
        return sprintf('%s/dashboard', $this->frontUrl);
    }

    public function getConfigUrl(): string
    {
        return sprintf('%s/config', $this->frontUrl);
    }

    public function getCampaignSmsCreateUrl(): string
    {
        return sprintf('%s/campaign/sms/create', $this->frontUrl);
    }

    public function getSubscriptionPaymentSuccessUrl(?string $referer = null): string
    {
        $url = sprintf('%s/register/step/3', $this->frontUrl);
        if ($referer) {
            $url = sprintf('%s?referer=%s', $url, $referer);
        }

        return $url;
    }

    public function getSubscriptionPaymentCancelUrl(): string
    {
        return sprintf('%s/register/step/2', $this->frontUrl);
    }

    public function getSmsPaymentSuccessUrl(?string $referer = null): string
    {
        return match ($referer) {
            'dashboard' => sprintf('%s/payment-sms?session_id={CHECKOUT_SESSION_ID}', $this->frontUrl),
            'campaign_sms_create' => $this->getCampaignSmsCreateUrl(),
            default => sprintf('%s/register/step/4', $this->frontUrl),
        };
    }

    public function getSmsPaymentCancelUrl(?string $referer = null): string
    {
        return match ($referer) {
            'dashboard' => $this->getDashboardUrl(),
            'campaign_sms_create' => $this->getCampaignSmsCreateUrl(),
            default => sprintf('%s/register/step/3', $this->frontUrl),
        };
    }

    public function getRichSmsUrl(Sms $sms): string
    {
        return sprintf('%s/rich-sms/%s?token=%s', $this->frontUrl, $sms->getId(), $this->richSmsManager->createToken($sms));
    }

    public function getResetPasswordUrl(AbstractPasswordToken $passwordToken): string
    {
        return sprintf('%s/forgot-password/%s', $this->frontUrl, $passwordToken->getToken());
    }

    public function getLoginLink(string $token): string
    {
        return sprintf('%s/login-link?token=%s', $this->frontUrl, $token);
    }

    public function getCreateCustomerAccountUrl(Company $company): string
    {
        return sprintf('%s/create-customer-account/%s?token=%s', $this->frontUrl, $company->getId(), CreateCustomerWithTokenInfosController::createToken($company));
    }
}
